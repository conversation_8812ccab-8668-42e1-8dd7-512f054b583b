"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for BoPhanSuDungCCDC model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from django.core.exceptions import ValidationError
from rest_framework import permissions, status  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401,
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    BoPhanSuDungCCDCSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.services.danh_muc import (  # noqa: F401,
    BoPhanSuDungCCDCService,
)


@extend_schema_view(
    list=extend_schema(
        summary="List BoPhanSuDungCCDC",
        description="Retrieve a list of BoPhanSuDungCCDC (Department for Tool and Equipment Usage) instances.",  # noqa: E501
        tags=["BoPhanSuDungCCDC"],
    ),
    create=extend_schema(
        summary="Create BoPhanSuDungCCDC",
        description="Create a new BoPhanSuDungCCDC (Department for Tool and Equipment Usage) instance.",  # noqa: E501
        tags=["BoPhanSuDungCCDC"],
    ),
    retrieve=extend_schema(
        summary="Retrieve BoPhanSuDungCCDC",
        description="Retrieve a specific BoPhanSuDungCCDC (Department for Tool and Equipment Usage) instance.",  # noqa: E501
        tags=["BoPhanSuDungCCDC"],
    ),
    update=extend_schema(
        summary="Update BoPhanSuDungCCDC",
        description="Update a specific BoPhanSuDungCCDC (Department for Tool and Equipment Usage) instance.",  # noqa: E501
        tags=["BoPhanSuDungCCDC"],
    ),
    destroy=extend_schema(
        summary="Delete BoPhanSuDungCCDC",
        description="Delete a specific BoPhanSuDungCCDC (Department for Tool and Equipment Usage) instance.",  # noqa: E501
        tags=["BoPhanSuDungCCDC"],
    ),
)
class BoPhanSuDungCCDCViewSet(EntityRelatedViewSet):
    """
    A ViewSet for BoPhanSuDungCCDC model.
    """

    serializer_class = BoPhanSuDungCCDCSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = BoPhanSuDungCCDCService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for this ViewSet.

        Returns
        -------
        QuerySet
            The queryset of BoPhanSuDungCCDC instances
        """
        entity_slug = self.kwargs.get('entity_slug')
        search_query = self.request.query_params.get('search', None)
        status_filter = self.request.query_params.get('status', None)
        return self.service.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status_filter,
        )

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new BoPhanSuDungCCDC instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response
        """
        entity_slug = kwargs.get('entity_slug')
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            # Create the instance using the service
            instance = self.service.create(
                entity_slug=entity_slug, data=serializer.validated_data
            )

            # Return the created instance
            response_serializer = self.get_serializer(instance)
            return Response(
                response_serializer.data, status=status.HTTP_201_CREATED
            )
        except ValidationError as e:
            return Response(
                {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a BoPhanSuDungCCDC instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response
        """
        entity_slug = kwargs.get('entity_slug')
        uuid = kwargs.get('uuid')
        serializer = self.get_serializer(
            data=request.data, partial=kwargs.get('partial', False)
        )
        serializer.is_valid(raise_exception=True)

        try:
            # Update the instance using the service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=uuid,
                data=serializer.validated_data,
            )

            if instance is None:
                return Response(
                    {'detail': 'BoPhanSuDungCCDC not found.'},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Return the updated instance
            response_serializer = self.get_serializer(instance)
            return Response(response_serializer.data)
        except ValidationError as e:
            return Response(
                {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a BoPhanSuDungCCDC instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response
        """
        entity_slug = kwargs.get('entity_slug')
        uuid = kwargs.get('uuid')
        # Delete the instance using the service
        deleted = self.service.delete(entity_slug=entity_slug, uuid=uuid)
        if not deleted:
            return Response(
                {'detail': 'BoPhanSuDungCCDC not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        return Response(status=status.HTTP_204_NO_CONTENT)
