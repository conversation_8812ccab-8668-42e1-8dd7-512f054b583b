"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Simple API Error Handling Decorators for Django Ledger.
Purpose: Wrap views to prevent 500 errors and provide clear error responses.
"""

import logging
from functools import wraps
from typing import Any, Dict, Optional

from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import IntegrityError
from rest_framework import status
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.response import Response

logger = logging.getLogger(__name__)


def format_error_response(
    error_message: str,
    error_code: str = "INTERNAL_ERROR",
    details: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Format standardized error response.

    Parameters
    ----------
    error_message : str
        The error message
    error_code : str
        The error code
    details : Optional[Dict[str, Any]]
        Additional error details

    Returns
    -------
    Dict[str, Any]
        Formatted error response
    """
    from datetime import datetime

    response = {
        "status": "error",
        "error_code": error_code,
        "message": error_message,
        "timestamp": datetime.now().isoformat()
    }

    if details:
        response["details"] = details

    return response


def api_exception_handler(func):
    """
    Simple decorator to prevent 500 errors and provide clear error responses.

    Purpose: Wrap view methods to catch exceptions and return proper HTTP status codes
    instead of letting them bubble up as 500 Internal Server Error.

    Parameters
    ----------
    func : callable
        The view method to wrap

    Returns
    -------
    callable
        The wrapped function
    """
    @wraps(func)
    def wrapper(self, request, *args, **kwargs):
        try:
            return func(self, request, *args, **kwargs)

        except DRFValidationError as e:
            # DRF validation errors - let DRF handle these naturally
            raise e

        except DjangoValidationError as e:
            # Django validation errors
            logger.warning(f"Django Validation Error in {func.__name__}: {str(e)}")
            details = {}
            if hasattr(e, 'message_dict'):
                details = e.message_dict
            elif hasattr(e, 'messages'):
                details = {"non_field_errors": e.messages}

            return Response(
                format_error_response(
                    error_message="Validation failed",
                    error_code="VALIDATION_ERROR",
                    details=details
                ),
                status=status.HTTP_400_BAD_REQUEST
            )

        except IntegrityError as e:
            # Database integrity errors - prevent 500
            logger.error(f"Integrity Error in {func.__name__}: {str(e)}")

            if "UNIQUE constraint failed" in str(e) or "duplicate key value" in str(e):
                error_message = "A record with this information already exists"
                error_code = "DUPLICATE_RESOURCE"
                status_code = status.HTTP_409_CONFLICT
            elif "NOT NULL constraint failed" in str(e):
                error_message = "Required field is missing"
                error_code = "MISSING_REQUIRED_FIELD"
                status_code = status.HTTP_400_BAD_REQUEST
            elif "FOREIGN KEY constraint failed" in str(e):
                error_message = "Referenced record does not exist"
                error_code = "INVALID_REFERENCE"
                status_code = status.HTTP_400_BAD_REQUEST
            else:
                error_message = "Data integrity constraint violation"
                error_code = "INTEGRITY_ERROR"
                status_code = status.HTTP_400_BAD_REQUEST

            return Response(
                format_error_response(
                    error_message=error_message,
                    error_code=error_code
                ),
                status=status_code
            )

        except ValueError as e:
            # Value errors from business logic
            logger.warning(f"Value Error in {func.__name__}: {str(e)}")
            return Response(
                format_error_response(
                    error_message=str(e),
                    error_code="INVALID_VALUE"
                ),
                status=status.HTTP_400_BAD_REQUEST
            )

        except PermissionError as e:
            # Permission errors
            logger.warning(f"Permission Error in {func.__name__}: {str(e)}")
            return Response(
                format_error_response(
                    error_message="You don't have permission to perform this action",
                    error_code="PERMISSION_DENIED"
                ),
                status=status.HTTP_403_FORBIDDEN
            )

        except Exception as e:
            # Catch-all to prevent 500 errors
            logger.error(f"Unexpected Error in {func.__name__}: {str(e)}", exc_info=True)
            return Response(
                format_error_response(
                    error_message="An unexpected error occurred",
                    error_code="INTERNAL_ERROR"
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    return wrapper
