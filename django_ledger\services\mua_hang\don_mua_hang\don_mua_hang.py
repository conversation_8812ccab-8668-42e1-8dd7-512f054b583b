"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from datetime import datetime  # noqa: F401
from decimal import Decimal  # noqa: F401
from typing import Any, Dict, List, Optional  # noqa: F401

from django.core.exceptions import ValidationError  # noqa: F401
from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models import EntityModel  # noqa: F401,
from django_ledger.models.mua_hang.don_mua_hang.chi_tiet_don_mua_hang import (  # noqa: F401,
    ChiTietDonMuaHangModel,
)
from django_ledger.models.mua_hang.don_mua_hang.don_mua_hang import (  # noqa: F401,
    DonMuaHangModel,
)
from django_ledger.repositories.mua_hang.don_mua_hang.chi_tiet_don_mua_hang import (  # noqa: F401,
    ChiTietDonMuaHangRepository,
)
from django_ledger.repositories.mua_hang.don_mua_hang.don_mua_hang import (  # noqa: F401,
    DonMuaHangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,


class DonMuaHangService(BaseService):
    """
    Service class for handling DonMuaHang model business logic
    """

    def __init__(self, entity_slug: str, user_model):  # noqa: C901
        self.entity_slug = entity_slug
        self.user_model = user_model
        self.repository = DonMuaHangRepository()
        self.chi_tiet_repository = ChiTietDonMuaHangRepository()
        super().__init__()

    def _get_entity_model(self, entity_slug: str) -> EntityModel:  # noqa: C901
        """
        Helper method to get entity model from slug
        """
        return get_object_or_404(EntityModel, slug=entity_slug)

    def validate_don_mua_hang_data(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Validate đơn mua hàng data before creating/updating
        """
        # Remove entity_model if present
        if 'entity_model' in data:
            del data['entity_model']

        # Validate required fields
        required_fields = [
            'ma_ncc',
            'mst',
            'ten_ncc',
            'dia_chi',
            'ma_nvmh',
            'ma_nt',
            'dien_giai',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'ngay_hl',
            'ty_gia',
            't_so_luong',
            't_tien',
            't_thue',
            't_tt',
        ]
        for field in required_fields:
            if field not in data:
                raise ValueError(f'{field} is required')

        # Validate dates
        ngay_ct = data.get('ngay_ct')
        ngay_lct = data.get('ngay_lct')
        ngay_hl = data.get('ngay_hl')
        if isinstance(ngay_ct, str):
            ngay_ct = datetime.strptime(ngay_ct, '%Y-%m-%d').date()
        if isinstance(ngay_lct, str):
            ngay_lct = datetime.strptime(ngay_lct, '%Y-%m-%d').date()
        if isinstance(ngay_hl, str):
            ngay_hl = datetime.strptime(ngay_hl, '%Y-%m-%d').date()
        if ngay_lct > ngay_ct:
            raise ValueError('Ngày lập chứng từ không thể sau ngày chứng từ')
        if ngay_ct > ngay_hl:
            raise ValueError('Ngày chứng từ không thể sau ngày hiệu lực')

        # Validate monetary amounts
        t_tien = Decimal(str(data.get('t_tien')))
        t_thue = Decimal(str(data.get('t_thue')))
        t_tt = Decimal(str(data.get('t_tt')))
        if t_tien + t_thue != t_tt:
            raise ValueError(
                'Tổng thanh toán phải bằng tổng tiền cộng tổng thuế'
            )

        # Set default values
        if 'status' not in data:
            data['status'] = "1"
        if 'ma_nt' not in data:
            data['ma_nt'] = "VND"
        return data

    def get_don_mua_hang(
        self, entity_slug: str, uuid: str
    ) -> Optional[DonMuaHangModel]:  # noqa: C901
        """
        Get a đơn mua hàng by UUID for a specific entity
        """
        return (
            self.repository.get_queryset()
            .filter(entity_model__slug=entity_slug, uuid=uuid)
            .first()
        )

    def get_don_mua_hang_by_so_ct(
        self, entity_slug: str, so_ct: str
    ) -> Optional[DonMuaHangModel]:  # noqa: C901
        """
        Get a đơn mua hàng by so_ct for a specific entity
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_by_so_ct(entity_model, so_ct)

    def get_all_don_mua_hang(
        self, entity_slug: str, user_model, **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Get all đơn mua hàng for a specific entity with optional filters
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_queryset().filter(
            entity_model=entity_model, **kwargs
        )

    def get_active_don_mua_hang(
        self, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Get all active đơn mua hàng for a specific entity
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_active(entity_model)

    def create_don_mua_hang(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> DonMuaHangModel:  # noqa: C901
        """
        Create a new đơn mua hàng for a specific entity
        """
        # Get entity model instance
        entity_model = self._get_entity_model(entity_slug)
        # Extract chi_tiet data if present
        chi_tiet_data = data.pop('chi_tiet', None)
        # Validate data
        validated_data = self.validate_don_mua_hang_data(data)
        # Add entity model to validated data
        validated_data['entity_model'] = entity_model
        # Check if so_ct already exists for this entity
        existing = self.repository.get_by_so_ct(
            entity_model, validated_data['so_ct']
        )
        if existing:
            raise ValueError('Số chứng từ đã tồn tại cho entity này')

        # Handle foreign key references if they are UUIDs
        fk_fields = [
            'ma_ncc',
            'ma_nvmh',
            'han_thanh_toan',
            'ma_nt',
            'ma_kho',
            'phuong_thuc_thanh_toan',
            'noi_nhan',
        ]

        for field in fk_fields:
            if field in validated_data and isinstance(
                validated_data[field], str
            ):
                # Try to handle as UUID
                try:
                    from uuid import UUID  # noqa: F401,

                    # Check if it's a valid UUID
                    UUID(validated_data[field])
                    # No need to do anything, Django will handle UUID references
                except ValueError:
                    # If not a UUID, it might be a code reference
                    # We'll keep the current behavior for ma_kho as an example
                    if field == 'ma_kho':
                        from django_ledger.models import (  # noqa: F401,
                            KhoHangModel,
                        )

                        try:
                            kho_hang = KhoHangModel.objects.filter(
                                entity_model=entity_model,
                                ma_kho=validated_data['ma_kho'],
                            ).first()
                            if kho_hang:
                                validated_data['ma_kho'] = kho_hang
                            else:
                                raise ValueError(
                                    f"Không tìm thấy kho hàng với mã: {validated_data['ma_kho']}"
                                )  # noqa: E501
                        except Exception as e:
                            raise ValidationError(
                                f"Error processing kho: {str(e)}"
                            )

        # Use transaction to ensure all operations succeed or fail together
        with transaction.atomic():
            # Create đơn mua hàng
            instance = self.repository.create(**validated_data)
            # Create chi tiết if provided
            if (
                chi_tiet_data
                and isinstance(chi_tiet_data, list)
                and len(chi_tiet_data) > 0
            ):
                self.chi_tiet_repository.bulk_create(
                    entity_model=entity_model,
                    don_mua_hang=instance,
                    chi_tiet_list=chi_tiet_data,
                )

        return instance

    def update_don_mua_hang(
        self, entity_slug: str, uuid: str, data: Dict[str, Any]
    ) -> Optional[DonMuaHangModel]:  # noqa: C901
        """
        Update an existing đơn mua hàng for a specific entity
        """
        # Extract chi_tiet data if present
        chi_tiet_data = data.pop('chi_tiet', None)
        # Validate data
        validated_data = self.validate_don_mua_hang_data(data)
        # Get instance and check entity
        instance = self.get_don_mua_hang(entity_slug, uuid)
        if not instance:
            raise ValueError('Đơn mua hàng không tồn tại')

        if instance.entity_model.slug != entity_slug:
            raise ValueError('Đơn mua hàng không thuộc về entity này')

        # Check if new so_ct already exists (if being changed)
        if (
            'so_ct' in validated_data
            and validated_data['so_ct'] != instance.so_ct
        ):
            entity_model = self._get_entity_model(entity_slug)
            existing = self.repository.get_by_so_ct(
                entity_model, validated_data['so_ct']
            )
            if existing and existing.uuid != instance.uuid:
                raise ValueError('Số chứng từ đã tồn tại cho entity này')

        # Use transaction to ensure all operations succeed or fail together
        with transaction.atomic():
            # Update instance
            for key, value in validated_data.items():
                setattr(instance, key, value)
            instance.save()

            # Update chi tiết if provided
            if chi_tiet_data is not None:
                if isinstance(chi_tiet_data, list):
                    self.chi_tiet_repository.bulk_update(
                        entity_model=instance.entity_model,
                        don_mua_hang=instance,
                        chi_tiet_list=chi_tiet_data,
                    )
                elif chi_tiet_data == []:
                    # Empty list means delete all chi tiết
                    self.chi_tiet_repository.delete_by_don_mua_hang(
                        instance.uuid
                    )

        return instance

    def delete_don_mua_hang(
        self, entity_slug: str, uuid: str
    ) -> bool:  # noqa: C901
        """
        Delete a đơn mua hàng for a specific entity
        """
        instance = self.get_don_mua_hang(entity_slug, uuid)
        if not instance:
            raise ValueError('Đơn mua hàng không tồn tại')

        if instance.entity_model.slug != entity_slug:
            raise ValueError('Đơn mua hàng không thuộc về entity này')

        # Use transaction to ensure all operations succeed or fail together
        with transaction.atomic():
            # Delete all chi tiết first
            self.chi_tiet_repository.delete_by_don_mua_hang(instance.uuid)

            # Then delete the đơn mua hàng
            instance.delete()

        return True

    def get_don_mua_hang_by_date_range(
        self, entity_slug: str, start_date, end_date
    ) -> QuerySet:  # noqa: C901
        """
        Get đơn mua hàng within a date range
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_by_date_range(
            entity_model, start_date, end_date
        )

    def get_don_mua_hang_by_ma_kh(
        self, entity_slug: str, ma_kh: str
    ) -> QuerySet:  # noqa: C901
        """
        Get đơn mua hàng for a specific customer
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_by_ma_kh(entity_model, ma_kh)

    def get_chi_tiet_don_mua_hang(
        self, entity_slug: str, don_mua_hang_uuid: str
    ) -> QuerySet:  # noqa: C901
        """
        Get all chi tiết for a specific đơn mua hàng
        """
        # Verify the đơn mua hàng exists and belongs to the entity
        instance = self.get_don_mua_hang(entity_slug, don_mua_hang_uuid)
        if not instance:
            raise ValueError('Đơn mua hàng không tồn tại')

        if instance.entity_model.slug != entity_slug:
            raise ValueError('Đơn mua hàng không thuộc về entity này')

        # Get all chi tiết
        return self.chi_tiet_repository.get_by_don_mua_hang(don_mua_hang_uuid)
