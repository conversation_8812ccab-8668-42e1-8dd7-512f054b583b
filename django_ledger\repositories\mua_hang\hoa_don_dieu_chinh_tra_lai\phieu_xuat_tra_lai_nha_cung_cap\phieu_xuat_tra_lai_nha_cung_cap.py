"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuXuatTraLaiNhaCungCap (Supplier Return Note) model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models import EntityModel  # noqa: F401,
from django_ledger.models.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (  # noqa: F401,
    PhieuXuatTraLaiNhaCungCapModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuXuatTraLaiNhaCungCapRepository(BaseRepository):
    """
    Repository for handling PhieuXuatTraLaiNhaCungCap model database operations
    """

    def __init__(self):  # noqa: C901
        super().__init__(PhieuXuatTraLaiNhaCungCapModel)
        self.model = PhieuXuatTraLaiNhaCungCapModel

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model

        Returns
        -------
        QuerySet
            Base queryset for the model
        """
        return self.model.objects.all()

    def get_for_entity(
        self, entity_slug: str, user_model
    ) -> QuerySet:  # noqa: C901
        """
        Get supplier return notes for a specific entity

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model

        Returns
        -------
        QuerySet
            QuerySet of PhieuXuatTraLaiNhaCungCapModel instances
        """
        return self.model.objects.for_entity(
            entity_slug=entity_slug, user_model=user_model
        )

    def get_by_uuid(
        self, uuid: UUID
    ) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:  # noqa: C901
        """
        Get a supplier return note by UUID

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The supplier return note if found, None otherwise
        """
        try:
            return self.model.objects.get(uuid=uuid)
        except self.model.DoesNotExist:
            return None

    def create(
        self, entity_slug: str, user_model, data: Dict[str, Any]
    ) -> PhieuXuatTraLaiNhaCungCapModel:  # noqa: C901
        """
        Create a new supplier return note

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model
        data : Dict[str, Any]
            The data to create the supplier return note with

        Returns
        -------
        PhieuXuatTraLaiNhaCungCapModel
            The created supplier return note
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)
        # Add entity model to data
        data['entity_model'] = entity_model
        # Create supplier return note
        return self.model.objects.create(**data)

    def update(
        self, uuid: UUID, data: Dict[str, Any]
    ) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:  # noqa: C901
        """
        Update an existing supplier return note

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note to update
        data : Dict[str, Any]
            The data to update the supplier return note with

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The updated supplier return note if found, None otherwise
        """
        instance = self.get_by_uuid(uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a supplier return note

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note to delete

        Returns
        -------
        bool
            True if the supplier return note was deleted, False otherwise
        """
        instance = self.get_by_uuid(uuid)
        if instance:
            instance.delete()
            return True
        return False
