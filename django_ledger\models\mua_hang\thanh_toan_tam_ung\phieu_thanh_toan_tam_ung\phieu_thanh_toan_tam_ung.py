"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuThanhToanTamUng (Advance Payment Settlement Voucher) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, Q, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuThanhToanTamUngModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuThanhToanTamUngModel. This implements multiple methods or queries needed to get a  # noqa: E501
    filtered QuerySet based on the PhieuThanhToanTamUngModel status.
    """

    def active(self) -> QuerySet:  # noqa: C901
        """
        Active advance payment settlement vouchers can be viewed and processed.

        Returns
        -------
        PhieuThanhToanTamUngModelQueryset
            A QuerySet of active Advance Payment Settlement Vouchers.
        """
        return self.filter(status="1")

    def inactive(self) -> QuerySet:  # noqa: C901
        """
        Inactive advance payment settlement vouchers cannot be viewed or processed.

        Returns
        -------
        PhieuThanhToanTamUngModelQueryset
            A QuerySet of inactive Advance Payment Settlement Vouchers.
        """
        return self.filter(status="0")

    def for_entity(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Returns a QuerySet of PhieuThanhToanTamUngModel associated with a specific EntityModel.  # noqa: E501

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.

        Returns
        -------
        PhieuThanhToanTamUngModelQueryset
            A filtered QuerySet of PhieuThanhToanTamUngModels.
        """
        qs = self.filter(Q(entity_model__slug__exact=entity_slug))
        return qs


class PhieuThanhToanTamUngModelManager(Manager):
    """
    A custom defined PhieuThanhToanTamUngModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuThanhToanTamUngModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom PhieuThanhToanTamUngModelQueryset.
        """
        return PhieuThanhToanTamUngModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug, user_model):  # noqa: C901
        """
        Fetches a QuerySet of PhieuThanhToanTamUngModels that the UserModel has access to.  # noqa: E501
        May include PhieuThanhToanTamUngModels from multiple Entities.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        user_model
            Logged in and authenticated django UserModel instance.
        """
        qs = self.get_queryset()
        return qs.filter(entity_model__slug__exact=entity_slug)


class PhieuThanhToanTamUngModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuThanhToanTamUngModel database will inherit from.  # noqa: E501
    The PhieuThanhToanTamUngModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    entity_model: EntityModel
        The EntityModel associated with this Advance Payment Settlement Voucher.

    action: str
        The action type for this record.

    question_ids: str
        Question IDs related to this record.

    ma_ngv: str
        Business operation code.

    ma_kh: CustomerModel
        Customer code.

    ma_so_thue: str
        Tax code.

    ong_ba: str
        Contact person name.

    dia_chi: str
        Address.

    e_mail: str
        Email address.

    tk: AccountModel
        Account code.

    dien_giai: str
        Description.

    unit_id: int
        Unit ID.

    i_so_ct: str
        Internal document number.

    ma_nk: str
        Accounting operation code.

    so_ct: str
        Document number.

    ngay_ct: date
        Document date.

    ngay_lct: date
        Document creation date.

    ma_nt: NgoaiTeModel
        Currency code.

    ty_gia: Decimal
        Exchange rate.

    status: str
        Status.

    transfer_yn: bool
        Transfer status.

    t_tien_nt: Decimal
        Total amount in foreign currency.

    t_tien: Decimal
        Total amount.

    t_thue_nt: Decimal
        Total tax in foreign currency.

    t_thue: Decimal
        Total tax.

    t_tt_nt: Decimal
        Total payment in foreign currency.

    t_tt: Decimal
        Total payment.

    t_tien_cl_nt: Decimal
        Remaining amount in foreign currency.

    t_tien_cl: Decimal
        Remaining amount.

    qt_tu_yn: bool
        Automatic process flag.
    """

    uuid = models.UUIDField(
        default=uuid4,
        editable=False,
        primary_key=True,
        help_text=_("Unique identifier for the advance payment settlement voucher")
    )
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
        help_text=_("The entity this advance payment settlement voucher belongs to")
    )
    ma_ngv = models.CharField(
        max_length=50,
        verbose_name=_("Mã nghiệp vụ"),
        help_text=_("Business operation code for the advance payment settlement")
    )
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_("Mã khách hàng"),
        help_text=_("Customer associated with this advance payment settlement")
    )
    ma_so_thue = models.CharField(
        max_length=50,
        verbose_name=_("Mã số thuế"),
        blank=True,
        null=True,
        help_text=_("Tax identification number of the customer")
    )
    ong_ba = models.CharField(
        max_length=255,
        verbose_name=_("Ông/Bà"),
        blank=True,
        null=True,
        help_text=_("Contact person name for the advance payment settlement")
    )
    dia_chi = models.CharField(
        max_length=500,
        verbose_name=_("Địa chỉ"),
        blank=True,
        null=True,
        help_text=_("Address of the customer or contact person")
    )
    e_mail = models.CharField(
        max_length=255,
        verbose_name=_("Email"),
        blank=True,
        null=True,
        help_text=_("Email address for communication regarding this settlement")
    )
    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản"),
        help_text=_("Account used for this advance payment settlement")
    )
    dien_giai = models.CharField(
        max_length=500,
        verbose_name=_("Diễn giải"),
        blank=True,
        null=True,
        help_text=_("Description or explanation of the advance payment settlement")
    )
    unit_id = models.IntegerField(
        verbose_name=_("ID đơn vị"),
        help_text=_("Unit identifier for organizational purposes")
    )
    i_so_ct = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ nội bộ"),
        blank=True,
        null=True,
        help_text=_("Internal document number for tracking purposes")
    )
    ma_nk = models.CharField(
        max_length=50,
        verbose_name=_("Mã nghiệp vụ kế toán"),
        help_text=_("Accounting operation code for this settlement")
    )
    so_ct = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ"),
        unique=True,
        help_text=_("Document number - must be unique across all settlements")
    )
    ngay_ct = models.DateField(
        verbose_name=_("Ngày chứng từ"),
        help_text=_("Document date when the settlement was created")
    )
    ngay_lct = models.DateField(
        verbose_name=_("Ngày lập chứng từ"),
        help_text=_("Date when the document was actually prepared")
    )
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_("Mã ngoại tệ"),
        help_text=_("Foreign currency code used in this settlement")
    )
    ty_gia = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tỷ giá"),
        default=1.00,
        help_text=_("Exchange rate applied for currency conversion")
    )
    status = models.CharField(
        max_length=10,
        verbose_name=_("Trạng thái"),
        choices=[
            ('0', _('Inactive')),
            ('1', _('Active')),
            ('2', _('Completed')),
            ('3', _('Cancelled')),
        ],
        default='1',
        help_text=_("Current status of the advance payment settlement")
    )
    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_("Đã chuyển"),
        help_text=_("Indicates whether the settlement has been transferred")
    )
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền ngoại tệ"),
        default=0.00,
        help_text=_("Total amount in foreign currency")
    )
    t_tien = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền"),
        default=0.00,
        help_text=_("Total amount in base currency")
    )
    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thuế ngoại tệ"),
        default=0.00,
        help_text=_("Total tax amount in foreign currency")
    )
    t_thue = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thuế"),
        default=0.00,
        help_text=_("Total tax amount in base currency")
    )
    t_tt_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thanh toán ngoại tệ"),
        default=0.00,
        help_text=_("Total payment amount in foreign currency")
    )
    t_tt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thanh toán"),
        default=0.00,
        help_text=_("Total payment amount in base currency")
    )
    t_tien_cl_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền còn lại ngoại tệ"),
        default=0.00,
        help_text=_("Remaining amount in foreign currency")
    )
    t_tien_cl = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền còn lại"),
        default=0.00,
        help_text=_("Remaining amount in base currency")
    )
    qt_tu_yn = models.BooleanField(
        default=False,
        verbose_name=_("Quy trình tự động"),
        help_text=_("Indicates if this settlement uses automatic processing")
    )
    objects = PhieuThanhToanTamUngModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Phiếu Thanh Toán Tạm Ứng')
        verbose_name_plural = _('Phiếu Thanh Toán Tạm Ứng')
        ordering = ['-ngay_ct', '-created']
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_nk']),
            models.Index(fields=['so_ct']),
            models.Index(fields=['ngay_ct']),
            models.Index(fields=['status']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['transfer_yn']),
            models.Index(fields=['entity_model', 'status']),
            models.Index(fields=['entity_model', 'ngay_ct']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(t_tien__gte=0),
                name='%(app_label)s_%(class)s_t_tien_positive'
            ),
            models.CheckConstraint(
                check=models.Q(t_tien_nt__gte=0),
                name='%(app_label)s_%(class)s_t_tien_nt_positive'
            ),
            models.CheckConstraint(
                check=models.Q(ty_gia__gt=0),
                name='%(app_label)s_%(class)s_ty_gia_positive'
            ),
        ]

    def __str__(self):  # noqa: C901
        return f'{self.so_ct}: {self.dien_giai}'


class PhieuThanhToanTamUngModel(PhieuThanhToanTamUngModelAbstract):
    """
    Base Advance Payment Settlement Voucher Model Implementation
    """

    class Meta(PhieuThanhToanTamUngModelAbstract.Meta):
        abstract = False
        db_table = 'phieu_thanh_toan_tam_ung'
