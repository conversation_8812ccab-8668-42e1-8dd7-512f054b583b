# Generated by Django 4.2.10 on 2025-06-12 01:14

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0028_update_phieu_xuat_tra_lai_nha_cung_cap_fields'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='phieuthanhtoantamungmodel',
            options={'ordering': ['-ngay_ct', '-created'], 'verbose_name': '<PERSON>ếu <PERSON> Toán Tạm Ứng', 'verbose_name_plural': '<PERSON>ếu <PERSON> Toán Tạm Ứng'},
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='dia_chi',
            field=models.CharField(blank=True, help_text='Address of the customer or contact person', max_length=500, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='dien_giai',
            field=models.CharField(blank=True, help_text='Description or explanation of the advance payment settlement', max_length=500, null=True, verbose_name='<PERSON><PERSON><PERSON> gi<PERSON>i'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='e_mail',
            field=models.CharField(blank=True, help_text='Email address for communication regarding this settlement', max_length=255, null=True, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='entity_model',
            field=models.ForeignKey(help_text='The entity this advance payment settlement voucher belongs to', on_delete=django.db.models.deletion.CASCADE, to='django_ledger.entitymodel', verbose_name='Entity Model'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='i_so_ct',
            field=models.CharField(blank=True, help_text='Internal document number for tracking purposes', max_length=50, null=True, verbose_name='Số chứng từ nội bộ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ma_kh',
            field=models.ForeignKey(help_text='Customer associated with this advance payment settlement', on_delete=django.db.models.deletion.CASCADE, to='django_ledger.customermodel', verbose_name='Mã khách hàng'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ma_ngv',
            field=models.CharField(help_text='Business operation code for the advance payment settlement', max_length=50, verbose_name='Mã nghiệp vụ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ma_nk',
            field=models.CharField(help_text='Accounting operation code for this settlement', max_length=50, verbose_name='Mã nghiệp vụ kế toán'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ma_nt',
            field=models.ForeignKey(help_text='Foreign currency code used in this settlement', on_delete=django.db.models.deletion.CASCADE, to='django_ledger.ngoaitemodel', verbose_name='Mã ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ma_so_thue',
            field=models.CharField(blank=True, help_text='Tax identification number of the customer', max_length=50, null=True, verbose_name='Mã số thuế'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ngay_ct',
            field=models.DateField(help_text='Document date when the settlement was created', verbose_name='Ngày chứng từ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ngay_lct',
            field=models.DateField(help_text='Date when the document was actually prepared', verbose_name='Ngày lập chứng từ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ong_ba',
            field=models.CharField(blank=True, help_text='Contact person name for the advance payment settlement', max_length=255, null=True, verbose_name='Ông/Bà'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='qt_tu_yn',
            field=models.BooleanField(default=False, help_text='Indicates if this settlement uses automatic processing', verbose_name='Quy trình tự động'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='so_ct',
            field=models.CharField(help_text='Document number - must be unique across all settlements', max_length=50, unique=True, verbose_name='Số chứng từ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='status',
            field=models.CharField(choices=[('0', 'Inactive'), ('1', 'Active'), ('2', 'Completed'), ('3', 'Cancelled')], default='1', help_text='Current status of the advance payment settlement', max_length=10, verbose_name='Trạng thái'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_thue',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total tax amount in base currency', max_digits=18, verbose_name='Tổng thuế'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_thue_nt',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total tax amount in foreign currency', max_digits=18, verbose_name='Tổng thuế ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_tien',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total amount in base currency', max_digits=18, verbose_name='Tổng tiền'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_tien_cl',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Remaining amount in base currency', max_digits=18, verbose_name='Tổng tiền còn lại'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_tien_cl_nt',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Remaining amount in foreign currency', max_digits=18, verbose_name='Tổng tiền còn lại ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_tien_nt',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total amount in foreign currency', max_digits=18, verbose_name='Tổng tiền ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_tt',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total payment amount in base currency', max_digits=18, verbose_name='Tổng thanh toán'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='t_tt_nt',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total payment amount in foreign currency', max_digits=18, verbose_name='Tổng thanh toán ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='tk',
            field=models.ForeignKey(help_text='Account used for this advance payment settlement', on_delete=django.db.models.deletion.CASCADE, to='django_ledger.accountmodel', verbose_name='Tài khoản'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='transfer_yn',
            field=models.BooleanField(default=False, help_text='Indicates whether the settlement has been transferred', verbose_name='Đã chuyển'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='ty_gia',
            field=models.DecimalField(decimal_places=2, default=1.0, help_text='Exchange rate applied for currency conversion', max_digits=18, verbose_name='Tỷ giá'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='unit_id',
            field=models.IntegerField(help_text='Unit identifier for organizational purposes', verbose_name='ID đơn vị'),
        ),
        migrations.AlterField(
            model_name='phieuthanhtoantamungmodel',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the advance payment settlement voucher', primary_key=True, serialize=False),
        ),
        migrations.AddIndex(
            model_name='phieuthanhtoantamungmodel',
            index=models.Index(fields=['ma_kh'], name='phieu_thanh_ma_kh_i_3ce02e_idx'),
        ),
        migrations.AddIndex(
            model_name='phieuthanhtoantamungmodel',
            index=models.Index(fields=['transfer_yn'], name='phieu_thanh_transfe_847011_idx'),
        ),
        migrations.AddIndex(
            model_name='phieuthanhtoantamungmodel',
            index=models.Index(fields=['entity_model', 'status'], name='phieu_thanh_entity__60e36a_idx'),
        ),
        migrations.AddIndex(
            model_name='phieuthanhtoantamungmodel',
            index=models.Index(fields=['entity_model', 'ngay_ct'], name='phieu_thanh_entity__0e5fa8_idx'),
        ),
        migrations.AddConstraint(
            model_name='phieuthanhtoantamungmodel',
            constraint=models.CheckConstraint(check=models.Q(('t_tien__gte', 0)), name='django_ledger_phieuthanhtoantamungmodel_t_tien_positive'),
        ),
        migrations.AddConstraint(
            model_name='phieuthanhtoantamungmodel',
            constraint=models.CheckConstraint(check=models.Q(('t_tien_nt__gte', 0)), name='django_ledger_phieuthanhtoantamungmodel_t_tien_nt_positive'),
        ),
        migrations.AddConstraint(
            model_name='phieuthanhtoantamungmodel',
            constraint=models.CheckConstraint(check=models.Q(('ty_gia__gt', 0)), name='django_ledger_phieuthanhtoantamungmodel_ty_gia_positive'),
        ),
    ]
